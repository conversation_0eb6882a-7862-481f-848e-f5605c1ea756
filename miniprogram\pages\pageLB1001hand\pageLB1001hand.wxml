<view class="pageBox">
  <view class="head">
    <view class="return" catchtap="returnSetp">
      <image class="return-img" src="/images/fanhui.png"></image>
    </view>
    <text class="title">雷达感应智能门禁</text>
  </view>
  <view class="font-blue other-btn" catch:tap="toPageSetting">
    固件升级
    <view style="margin-right: auto;text-indent: 26rpx;">版本：V{{Version}}</view>
  </view>


  <view class="header" wx:if="{{doorShow}}">
    <view class="doorContainer">
      <image src="../../images/LB1001/men-1.png" class="door" />
      <image src="../../images/LB1001/men-left.png" style="{{openDoor_left}}" class="door_left" />
      <image src="../../images/LB1001/men-right.png" style="{{openDoor_right}}" class="door_right" />
      <!-- <image src="../../images/LB1001/men-left.png" class="door_left" bind:tap="connectOpen"/>
      <image src="../../images/LB1001/men-right.png" class="door_right" bind:tap="connectOpen"/> -->
    </view>
    <!--   -->
    <view class="statusText">当前状态：{{openValue}}</view>
  </view>


  <view class="radar" wx:if="{{!doorShow}}">
    <view class="radar-scan"></view>
    <view class="cm">{{distanceCM}}m</view>
  </view>


  <view class="open_and_close_door" style="margin-top:30rpx;">
    <view bind:tap="openAndClose_door" class="{{openValue=='开门'?'blue':''}}" data-status="1">开门</view>
    <!-- <view>关门</view> -->
    <view style="margin-left: auto;" bind:tap="openAndClose_door" class=" {{openValue=='关门'?'blue':''}}"
      data-status="0">关门</view>
  </view>

  <view class="item pb" style="margin-top:30rpx;">
    <view class="label" style="position: relative;">
      <view style="text-indent: 40rpx;">雷达开关</view>
      <!-- <view class="wenhao" capture-bind:tap="handleWenHao">?</view>
      <view class="bubbleToast" wx:if="{{showBubble}}" bind:tap="handleWenHao">
        <view>
          开启此功能:"开门" 和 "关门" 按钮仅具备临时开关门功能。关闭此功能，雷达感应功能将关闭。
        </view>
      </view> -->
    </view>
    <view style="margin-left: auto;margin-right: 0rpx;">
      <switch checked="{{checked}}" bindchange="switch1Change" bindchange="changeChecked" color="#32a4f5" />
    </view>
  </view>
  <view class="tip">(雷达已关闭，无法自动感应开关门)</view>

  <view class="card">
    <view>说明：</view>
    <view>1.关闭雷达后，只能通过小程序手动开关门禁，门禁等同于蓝牙门锁。</view>
    <view>2.关闭雷达后，门禁指示灯状态为循环 1秒亮 1 秒灭。</view>

  </view>







</view>