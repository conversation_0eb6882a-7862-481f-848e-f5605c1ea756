<!--pages/pageConnect/pageConnect.wxml-->
<view class="pagebox">
    <view class="head">
      <view class="return" bindtap="returnSetp"></view>
      <text class="title">雷达感应智能门禁</text>
    </view>
    <view class="body-tit" style="display: flex;place-items: center;">设备列表：
      <view class="text_2">下拉可刷新设备列表</view> 
    </view>
    <!-- 没设备的时候 提示 -->
    <view class='not-device' wx:if="{{deviceList.length == 0}}">
      <view >
        <image class="not-device-img" src="/images/refresh.png" mode="aspectFit"></image>
        <view class="not-device-text font-blue">没发现设备,下拉刷新</view>
      </view>
    </view>
    <!-- 设备显示区域 -->
    <view class="view_third" wx:if="{{deviceList.length > 0}}">
    <!-- 单个设备 -->
      <view class="device-line" hover-class='hover-btn' hover-stay-time="1500" wx:for="{{deviceList}}" wx:key="id" bind:tap="onSelectedDevice" data-item="{{item}}" data-index="{{item.id}}">
        <image class="dev-left-img" src="/images/LB1001/<EMAIL>"> </image>
        <view class="dev-right textOver">
          <view class="labelName">设备名称：</view>
          <view style="color: transparent;">ds</view>
          <view class=" textOver" style="font-size: 32rpx;">{{item.localName}}</view>
          <!-- <view wx:if="{{item.deviceId !== connectedDevice.deviceId }}" class="font-blue"  src="/images/bianji.png" >未连接</view> -->
        </view>
        <image class="dev-right-img" data-name="{{item.name}}" data-local-name="{{item.localName}}" capture-catch:tap="onShowUpdateName" src="/images/bianji.png"> </image>
      </view>
    </view>
    <!-- 底部文字 -->
    <view class="body-foter1" wx:if="{{deviceList.length == 0}}">请打开手机蓝牙，并将手机靠近设备 5 米范围内</view>
    <view class="body-foter2" wx:if="{{deviceList.length > 0}}">设备列表为当前手机蓝牙可以搜索到的雷达门禁,
      若没有搜到需要的设备，请将手机靠近设备 5 米范围
      内，刷新重试或重启设备</view>
    <block wx:if="{{hiddenmodalput === false}}">
      <modal hidden="{{hiddenmodalput}}" title="重命名" confirm-text="确定" cancel-text="取消" bindcancel="onCancel" bindconfirm="onConfirm">
        <input class="updateInput" type='text' placeholder="请输入内容" value="{{catchText}}" 
        bindinput="onInput" auto-focus maxlength="24"/>
        <view class="updateTip">设备重命名，最长 24 个字符</view>
      </modal>
    </block>
</view>